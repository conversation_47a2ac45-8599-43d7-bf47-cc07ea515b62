# Dify 系统架构文档

## 目录

1. [项目概述](#项目概述)
2. [整体架构](#整体架构)
3. [核心模块详解](#核心模块详解)
   - [前端架构](#1-前端架构-web-frontend)
   - [后端 API 架构](#2-后端-api-架构-backend-api)
   - [应用模式](#3-应用模式-app-modes)
   - [工作流引擎](#4-工作流引擎-workflow-engine)
   - [模型管理系统](#5-模型管理系统-model-management)
   - [RAG 系统](#6-rag-系统-retrieval-augmented-generation)
   - [数据模型](#7-数据模型-data-models)
4. [部署架构](#部署架构)
5. [安全架构](#安全架构)
6. [监控与可观测性](#监控与可观测性)
7. [技术特色](#技术特色)
8. [API 接口设计](#api-接口设计)
9. [核心算法与技术实现](#核心算法与技术实现)
10. [数据库设计详解](#数据库设计详解)
11. [性能优化策略](#性能优化策略)
12. [安全与合规](#安全与合规)
13. [扩展性与可维护性](#扩展性与可维护性)
14. [运维与部署](#运维与部署)

## 项目概述

Dify 是一个开源的 LLM 应用开发平台，提供直观的界面结合 AI 工作流、RAG 管道、Agent 能力、模型管理、可观测性功能等，让用户可以快速从原型到生产。

**核心特性**:
- 🔧 **可视化工作流**: 拖拽式工作流编辑器，支持复杂业务逻辑
- 🤖 **多模型支持**: 集成 100+ LLM 提供商，统一调用接口
- 📚 **企业级 RAG**: 完整的知识库管理和检索增强生成
- 🛠️ **丰富工具生态**: 50+ 内置工具，支持自定义工具开发
- 🔐 **企业级安全**: 多租户隔离、RBAC 权限控制、数据加密
- 📊 **可观测性**: 完整的监控、日志和追踪体系

**技术栈概览**:
- **前端**: Next.js + React + TypeScript + Tailwind CSS
- **后端**: Python + Flask + SQLAlchemy + Celery
- **数据库**: PostgreSQL + Redis + 向量数据库
- **部署**: Docker + Docker Compose + Kubernetes

## 整体架构

### 系统组件架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Dify 平台架构                              │
├─────────────────────────────────────────────────────────────────┤
│  前端层 (Web Frontend)                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   控制台界面      │  │   应用分享界面    │  │   API 文档界面   │  │
│  │   (Console)     │  │   (Share)       │  │   (API Docs)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  网关层 (Gateway)                                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Nginx 反向代理                            │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   API 服务       │  │   Worker 服务    │  │   插件守护进程    │  │
│  │   (Flask API)   │  │   (Celery)      │  │   (Plugin)      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  核心服务层 (Core Services)                                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   工作流引擎      │  │   模型管理器      │  │   RAG 引擎       │  │
│  │   (Workflow)    │  │   (Model Mgr)   │  │   (RAG Engine)  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   工具管理器      │  │   数据集服务      │  │   代码执行沙箱    │  │
│  │   (Tool Mgr)    │  │   (Dataset)     │  │   (Sandbox)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   PostgreSQL    │  │   Redis 缓存     │  │   向量数据库      │  │
│  │   (主数据库)      │  │   (Session)     │  │   (Vector DB)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   文件存储        │  │   SSRF 代理      │                      │
│  │   (File Store)  │  │   (Proxy)       │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. 前端架构 (Web Frontend)

**技术栈**: Next.js + React + TypeScript + Tailwind CSS

**主要模块**:
- **控制台界面** (`web/app/(commonLayout)`): 应用管理、数据集管理、工具配置
- **应用分享界面** (`web/app/(shareLayout)`): 聊天界面、工作流界面、完成界面
- **组件库** (`web/app/components`): 可复用的 UI 组件

**核心页面结构**:
```
web/app/
├── (commonLayout)/          # 主控制台布局
│   ├── app/                # 应用管理
│   ├── datasets/           # 数据集管理
│   ├── tools/              # 工具管理
│   └── plugins/            # 插件管理
├── (shareLayout)/          # 分享页面布局
│   ├── chat/               # 聊天应用
│   ├── workflow/           # 工作流应用
│   └── completion/         # 文本完成应用
└── components/             # 通用组件
    ├── workflow/           # 工作流编辑器
    ├── datasets/           # 数据集组件
    └── base/               # 基础组件
```

### 2. 后端 API 架构 (Backend API)

**技术栈**: Flask + SQLAlchemy + Celery + Redis

**主要模块**:

#### 2.1 控制器层 (`api/controllers`)
```
controllers/
├── console/                # 控制台 API
├── service_api/            # 服务 API (对外)
├── web/                    # Web 应用 API
├── files/                  # 文件处理 API
└── inner_api/              # 内部 API
```

#### 2.2 核心业务层 (`api/core`)
```
core/
├── app/                    # 应用核心逻辑
├── workflow/               # 工作流引擎
├── model_runtime/          # 模型运行时
├── rag/                    # RAG 检索增强生成
├── tools/                  # 工具管理
├── agent/                  # Agent 智能体
└── plugin/                 # 插件系统
```

#### 2.3 服务层 (`api/services`)
```
services/
├── app_service.py          # 应用服务
├── dataset_service.py      # 数据集服务
├── workflow_service.py     # 工作流服务
├── model_provider_service.py # 模型提供商服务
└── tools/                  # 工具相关服务
```

### 3. 应用模式 (App Modes)

Dify 支持多种应用模式:

```python
class AppMode(StrEnum):
    COMPLETION = "completion"      # 文本完成
    WORKFLOW = "workflow"          # 工作流
    CHAT = "chat"                 # 聊天
    ADVANCED_CHAT = "advanced-chat" # 高级聊天
    AGENT_CHAT = "agent-chat"     # Agent 聊天
    CHANNEL = "channel"           # 渠道
```

**应用模式特点**:
- **Completion**: 单次文本生成，适用于翻译、摘要等任务
- **Chat**: 多轮对话，支持上下文记忆
- **Workflow**: 可视化工作流，支持复杂的业务逻辑
- **Agent**: 智能体模式，支持工具调用和推理

### 4. 工作流引擎 (Workflow Engine)

**核心组件**:
- **图引擎** (`core/workflow/graph_engine`): 工作流执行引擎
- **节点系统** (`core/workflow/nodes`): 各种类型的工作流节点
- **变量系统** (`core/variables`): 工作流变量管理

**支持的节点类型**:
```python
class NodeType(StrEnum):
    START = "start"                    # 开始节点
    END = "end"                       # 结束节点
    LLM = "llm"                       # LLM 节点
    KNOWLEDGE_RETRIEVAL = "knowledge-retrieval"  # 知识检索
    IF_ELSE = "if-else"               # 条件判断
    CODE = "code"                     # 代码执行
    HTTP_REQUEST = "http-request"     # HTTP 请求
    TOOL = "tool"                     # 工具调用
    LOOP = "loop"                     # 循环节点
    PARAMETER_EXTRACTOR = "parameter-extractor"  # 参数提取
    # ... 更多节点类型
```

### 5. 模型管理系统 (Model Management)

**架构特点**:
- **统一接口**: 支持多种模型提供商的统一调用接口
- **负载均衡**: 支持模型的负载均衡配置
- **凭据管理**: 安全的 API 密钥管理

**支持的模型类型**:
- **LLM**: 大语言模型 (文本生成、对话)
- **Text Embedding**: 文本嵌入模型
- **Rerank**: 重排序模型
- **Speech-to-text**: 语音转文本
- **Text-to-speech**: 文本转语音
- **Moderation**: 内容审核模型

### 6. RAG 系统 (Retrieval-Augmented Generation)

**核心组件**:
- **文档处理** (`core/rag/extractor`): 支持 PDF、Word、PPT 等格式
- **文本分割** (`core/rag/splitter`): 智能文档分割
- **向量化** (`core/rag/embedding`): 文档向量化存储
- **检索器** (`core/rag/retrieval`): 多种检索策略

**支持的向量数据库**:
- Weaviate, Qdrant, Milvus, Chroma
- PGVector, OpenSearch, Elasticsearch
- 云服务: Pinecone, Zilliz 等

### 7. 数据模型 (Data Models)

**核心实体关系**:
```
Tenant (租户)
├── Account (用户账户)
├── App (应用)
│   ├── AppModelConfig (应用模型配置)
│   ├── Workflow (工作流)
│   ├── Conversation (对话)
│   └── Message (消息)
├── Dataset (数据集)
│   ├── Document (文档)
│   └── DocumentSegment (文档片段)
└── Provider (模型提供商)
    └── ProviderModel (提供商模型)
```

## 部署架构

### Docker Compose 部署

**核心服务**:
- **api**: Flask API 服务
- **worker**: Celery 后台任务处理
- **web**: Next.js 前端服务
- **db**: PostgreSQL 数据库
- **redis**: Redis 缓存和消息队列
- **nginx**: 反向代理和负载均衡
- **sandbox**: 代码执行沙箱
- **plugin_daemon**: 插件守护进程

**可选服务**:
- **weaviate/qdrant**: 向量数据库
- **ssrf_proxy**: SSRF 防护代理
- **certbot**: SSL 证书管理

### 扩展性设计

**水平扩展**:
- API 服务支持多实例部署
- Worker 服务支持动态扩缩容
- 数据库支持读写分离

**插件系统**:
- 支持自定义工具插件
- 支持第三方模型提供商集成
- 支持自定义数据源连接器

## 安全架构

### 认证与授权
- **多租户隔离**: 基于 tenant_id 的数据隔离
- **RBAC 权限控制**: 基于角色的访问控制
- **API 密钥管理**: 安全的 API 访问控制

### 数据安全
- **敏感数据加密**: 数据库敏感字段加密存储
- **SSRF 防护**: 防止服务器端请求伪造攻击
- **代码沙箱**: 隔离的代码执行环境

## 监控与可观测性

### 日志系统
- **结构化日志**: 统一的日志格式和级别
- **分布式追踪**: 支持 OpenTelemetry 集成
- **错误监控**: Sentry 错误追踪集成

### 性能监控
- **应用性能监控**: API 响应时间、吞吐量监控
- **资源监控**: CPU、内存、磁盘使用监控
- **业务指标**: 应用调用次数、用户活跃度等

## 技术特色

### 1. 可视化工作流编辑器
- 拖拽式节点编辑
- 实时预览和调试
- 版本管理和回滚

### 2. 多模型统一接口
- 支持 100+ 模型提供商
- 统一的调用接口和参数
- 智能路由和负载均衡

### 3. 企业级 RAG 能力
- 多种文档格式支持
- 智能分割和向量化
- 混合检索策略

### 4. 丰富的工具生态
- 50+ 内置工具
- 支持自定义工具开发
- API 工具自动集成

## API 接口设计

### RESTful API 架构

**API 分层设计**:
```
/console/api/          # 控制台管理 API
├── workspaces/        # 工作空间管理
├── apps/              # 应用管理
├── datasets/          # 数据集管理
├── models/            # 模型管理
└── tools/             # 工具管理

/v1/                   # 服务 API (对外)
├── chat-messages      # 聊天消息
├── completion-messages # 完成消息
├── workflows/runs     # 工作流执行
├── files/             # 文件上传
└── parameters         # 参数获取

/files/                # 文件服务 API
├── upload             # 文件上传
├── image_preview      # 图片预览
└── tools/             # 工具文件
```

### 数据流架构

**请求处理流程**:
```
用户请求 → Nginx → Flask API → 业务逻辑 → 数据库/缓存
                              ↓
                         Celery Worker → 异步任务处理
                              ↓
                         模型调用/工具执行
                              ↓
                         结果返回/存储
```

## 核心算法与技术实现

### 1. 工作流执行引擎

**图执行算法**:
- **拓扑排序**: 确定节点执行顺序
- **并行执行**: 支持无依赖节点并行处理
- **条件分支**: 基于条件的动态路径选择
- **循环控制**: 支持 for 循环和条件循环

**状态管理**:
```python
class WorkflowExecutionStatus(StrEnum):
    RUNNING = "running"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    STOPPED = "stopped"
    PAUSED = "paused"
```

### 2. RAG 检索算法

**检索策略**:
- **语义检索**: 基于向量相似度的检索
- **关键词检索**: 基于 BM25 的全文检索
- **混合检索**: 语义+关键词的融合检索
- **重排序**: 使用 Rerank 模型优化结果

**向量化流程**:
```
文档输入 → 文本提取 → 智能分割 → 向量化 → 存储到向量数据库
                                    ↓
检索查询 → 查询向量化 → 相似度计算 → 结果排序 → 返回相关片段
```

### 3. 模型调用优化

**负载均衡策略**:
- **轮询**: 平均分配请求
- **加权轮询**: 基于模型性能权重分配
- **最少连接**: 选择当前连接数最少的实例

**缓存机制**:
- **结果缓存**: 相同输入的结果缓存
- **模型缓存**: 模型实例的内存缓存
- **配置缓存**: 模型配置的 Redis 缓存

## 数据库设计详解

### 核心表结构

**应用相关表**:
```sql
-- 应用表
apps (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    mode VARCHAR(255) NOT NULL,  -- completion/chat/workflow/agent-chat
    workflow_id UUID,
    app_model_config_id UUID,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 工作流表
workflows (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    app_id UUID NOT NULL,
    type VARCHAR(255) NOT NULL,  -- workflow/chat
    version VARCHAR(255) NOT NULL,
    graph TEXT,  -- JSON 格式的工作流图
    features TEXT,  -- JSON 格式的功能配置
    created_at TIMESTAMP
);

-- 工作流运行记录
workflow_runs (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    app_id UUID NOT NULL,
    workflow_id UUID NOT NULL,
    status VARCHAR(255) NOT NULL,
    inputs TEXT,  -- JSON 格式的输入
    outputs TEXT,  -- JSON 格式的输出
    elapsed_time FLOAT,
    created_at TIMESTAMP
);
```

**数据集相关表**:
```sql
-- 数据集表
datasets (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    provider VARCHAR(255) NOT NULL,  -- vendor/external
    data_source_type VARCHAR(255),
    indexing_technique VARCHAR(255),  -- high_quality/economy
    created_at TIMESTAMP
);

-- 文档表
documents (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    dataset_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    data_source_type VARCHAR(255),
    data_source_info TEXT,  -- JSON 格式
    dataset_process_rule_id UUID,
    batch VARCHAR(255),
    created_at TIMESTAMP
);

-- 文档片段表
document_segments (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    dataset_id UUID NOT NULL,
    document_id UUID NOT NULL,
    position INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER,
    tokens INTEGER,
    keywords TEXT[],
    index_node_id VARCHAR(255),
    index_node_hash VARCHAR(255),
    hit_count INTEGER DEFAULT 0,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP
);
```

### 索引优化策略

**关键索引**:
```sql
-- 应用查询优化
CREATE INDEX idx_apps_tenant_id ON apps(tenant_id);
CREATE INDEX idx_apps_mode ON apps(mode);

-- 工作流查询优化
CREATE INDEX idx_workflows_app_id ON workflows(app_id);
CREATE INDEX idx_workflow_runs_status ON workflow_runs(status);

-- 数据集查询优化
CREATE INDEX idx_datasets_tenant_id ON datasets(tenant_id);
CREATE INDEX idx_documents_dataset_id ON documents(dataset_id);
CREATE INDEX idx_document_segments_document_id ON document_segments(document_id);
CREATE INDEX idx_document_segments_enabled ON document_segments(enabled);
```

## 性能优化策略

### 1. 缓存策略

**多层缓存架构**:
```
应用层缓存 (内存) → Redis 缓存 → 数据库
```

**缓存类型**:
- **查询结果缓存**: 频繁查询的数据库结果
- **模型响应缓存**: LLM 相同输入的响应结果
- **配置缓存**: 应用配置、模型配置等
- **会话缓存**: 用户会话和上下文信息

### 2. 异步处理

**Celery 任务队列**:
```python
# 异步任务类型
- 文档索引任务 (document_indexing_task)
- 数据集处理任务 (dataset_processing_task)
- 邮件发送任务 (mail_sending_task)
- 清理任务 (cleanup_tasks)
```

**任务优先级**:
- **高优先级**: 用户交互相关任务
- **中优先级**: 数据处理任务
- **低优先级**: 清理和维护任务

### 3. 数据库优化

**连接池配置**:
```python
SQLALCHEMY_POOL_SIZE = 30
SQLALCHEMY_POOL_RECYCLE = 3600
SQLALCHEMY_MAX_OVERFLOW = 60
```

**查询优化**:
- **分页查询**: 大数据集的分页处理
- **预加载**: 关联数据的预加载
- **索引优化**: 基于查询模式的索引设计

## 安全与合规

### 1. 数据安全

**加密策略**:
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 敏感数据 AES-256 加密
- **密钥管理**: 分层密钥管理体系

**数据隔离**:
- **租户隔离**: 基于 tenant_id 的严格数据隔离
- **用户隔离**: 基于用户权限的数据访问控制
- **环境隔离**: 开发/测试/生产环境隔离

### 2. 访问控制

**认证机制**:
- **JWT Token**: 无状态的用户认证
- **API Key**: 服务间认证
- **OAuth 2.0**: 第三方集成认证

**权限模型**:
```python
class TenantAccountRole(StrEnum):
    OWNER = "owner"        # 所有者
    ADMIN = "admin"        # 管理员
    EDITOR = "editor"      # 编辑者
    NORMAL = "normal"      # 普通用户
    DATASET_OPERATOR = "dataset_operator"  # 数据集操作员
```

### 3. 审计与监控

**操作审计**:
- **用户操作日志**: 记录所有用户操作
- **API 调用日志**: 记录所有 API 调用
- **数据变更日志**: 记录重要数据变更

**安全监控**:
- **异常检测**: 异常登录和操作检测
- **速率限制**: API 调用频率限制
- **威胁检测**: 恶意请求检测

## 扩展性与可维护性

### 1. 微服务化设计

**服务拆分**:
- **用户服务**: 用户认证和权限管理
- **应用服务**: 应用生命周期管理
- **工作流服务**: 工作流执行引擎
- **数据集服务**: 知识库管理
- **模型服务**: 模型调用和管理

### 2. 插件化架构

**插件类型**:
- **模型提供商插件**: 新的 LLM 提供商集成
- **工具插件**: 自定义工具和 API 集成
- **数据源插件**: 新的数据源连接器
- **存储插件**: 新的存储后端支持

### 3. 配置管理

**环境配置**:
```python
# 核心配置类别
- 数据库配置 (DB_*)
- Redis 配置 (REDIS_*)
- 存储配置 (STORAGE_*)
- 模型配置 (MODEL_*)
- 安全配置 (SECRET_*, ENCRYPTION_*)
```

**动态配置**:
- **功能开关**: 基于配置的功能启用/禁用
- **限流配置**: 动态调整 API 限流参数
- **模型配置**: 运行时模型参数调整

## 运维与部署

### 1. 容器化部署

**Docker 镜像**:
- **dify-api**: 后端 API 服务镜像
- **dify-web**: 前端 Web 服务镜像
- **dify-sandbox**: 代码执行沙箱镜像
- **dify-plugin-daemon**: 插件守护进程镜像

### 2. 高可用部署

**负载均衡**:
- **API 服务**: 多实例 + Nginx 负载均衡
- **数据库**: 主从复制 + 读写分离
- **缓存**: Redis 集群模式

**故障恢复**:
- **健康检查**: 服务健康状态监控
- **自动重启**: 服务异常自动重启
- **数据备份**: 定期数据备份和恢复

### 3. 监控告警

**监控指标**:
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 响应时间、错误率、吞吐量
- **业务指标**: 用户活跃度、API 调用量

**告警策略**:
- **阈值告警**: 基于指标阈值的告警
- **异常检测**: 基于机器学习的异常检测
- **链路追踪**: 分布式链路追踪和分析

## 总结

### 架构优势

1. **模块化设计**: 清晰的分层架构，各模块职责明确，便于维护和扩展
2. **高可扩展性**: 支持水平扩展，插件化架构支持功能扩展
3. **企业级安全**: 多层安全防护，满足企业级安全要求
4. **高性能**: 多级缓存、异步处理、数据库优化等性能优化策略
5. **易于部署**: 容器化部署，支持多种部署方式
6. **可观测性**: 完整的监控、日志和追踪体系

### 技术创新点

1. **统一模型接口**: 抽象了不同 LLM 提供商的差异，提供统一调用接口
2. **可视化工作流**: 直观的拖拽式工作流编辑器，降低开发门槛
3. **智能 RAG**: 多种检索策略和重排序算法，提升检索质量
4. **代码沙箱**: 安全的代码执行环境，支持多种编程语言
5. **插件生态**: 开放的插件架构，支持第三方扩展

### 适用场景

- **企业知识库**: 构建企业内部知识问答系统
- **客户服务**: 智能客服和自动化客户支持
- **内容生成**: 自动化内容创作和编辑
- **数据分析**: 基于自然语言的数据查询和分析
- **工作流自动化**: 复杂业务流程的自动化处理

### 发展方向

1. **多模态支持**: 扩展对图像、音频、视频等多模态数据的处理能力
2. **边缘计算**: 支持边缘设备部署，降低延迟和成本
3. **联邦学习**: 支持分布式模型训练和推理
4. **低代码平台**: 进一步降低开发门槛，支持非技术用户
5. **行业解决方案**: 针对特定行业提供定制化解决方案

这个架构文档全面展示了 Dify 平台的技术架构、设计理念和实现细节，为开发者、架构师和运维人员提供了完整的技术参考。通过深入理解这些架构设计，可以更好地使用、扩展和维护 Dify 平台。
